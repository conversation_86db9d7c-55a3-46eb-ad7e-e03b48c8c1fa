import {use<PERSON><PERSON>back, MouseEvent} from 'react';
import {useNavigate} from 'react-router-dom';
import {MCPDetailLink, MCPPlaygroundLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiPostViewCount} from '@/api/mcp';
import BaseMCPCard from '@/components/MCP/BaseMCPCard';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}
const SquireMCPCard = ({server, refresh}: Props) => {
    const navigate = useNavigate();

    const handleClick = useCallback(
        async () => {
            await apiPostViewCount({mcpServerId: server.id});
            navigate(MCPDetailLink.toUrl({mcpId: server.id}));
        },
        [navigate, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
        },
        [navigate, server.id]
    );

    const handlePlaygroundClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            window.open(MCPPlaygroundLink.toUrl({serverId: server.id}), '_blank');
        },
        [server.id]
    );

    const handleUseCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'tools', workspaceId: server.workspaceId});
            navigate(url);
        },
        [navigate, server]
    );

    return (
        <BaseMCPCard
            server={server}
            refresh={refresh}
            showDepartment
            onCardClick={handleClick}
            onViewCountClick={handleViewCountClick}
            onPlaygroundClick={handlePlaygroundClick}
            onUseCountClick={handleUseCountClick}
        />
    );
};

export default SquireMCPCard;
