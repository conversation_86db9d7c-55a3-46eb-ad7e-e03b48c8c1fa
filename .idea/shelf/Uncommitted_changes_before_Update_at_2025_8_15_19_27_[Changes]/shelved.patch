Index: src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {memo, MouseEvent, useCallback} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {Button} from '@panda-design/components';\nimport cx from 'classnames';\nimport {css} from '@emotion/css';\nimport {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';\nimport {MCPServerBase, MCPEditTab} from '@/types/mcp/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport BaseMCPCard from '@/components/MCP/BaseMCPCard';\nimport {actionButtonHoverStyle} from '@/components/MCP/BaseMCPCard/styles';\nimport {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';\n\nconst cardContainerStyle = css`\n    position: relative;\n    &:hover {\n        z-index: 2;\n    }\n`;\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\n\nconst SpaceMCPCard = ({server, refresh}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        () => {\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: MCPEditTab.Tools}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleViewCountClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            const url = MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview', workspaceId: spaceId});\n            navigate(url);\n        },\n        [navigate, server.id, spaceId]\n    );\n\n    const handleBasicInfoClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: MCPEditTab.ServerInfo}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleToolsConfigClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: MCPEditTab.Tools}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const renderActions = useCallback(\n        () => (\n            <>\n\n                <MCPSubscribeButton\n                    refresh={refresh}\n                    workspaceId={spaceId || server.workspaceId}\n                    id={server.id}\n                    className={cx(actionButtonHoverStyle)}\n                    showText={false}\n                />\n                <Button type=\"text\" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>\n                    基本信息\n                </Button>\n                <Button type=\"text\" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>\n                    工具配置\n                </Button>\n            </>\n        ),\n        [handleBasicInfoClick, handleToolsConfigClick, refresh, spaceId, server.workspaceId, server.id]\n    );\n\n    return (\n        <div className={cardContainerStyle}>\n            <BaseMCPCard\n                server={server}\n                refresh={refresh}\n                showDepartment={false}\n                workspaceId={spaceId}\n                onCardClick={handleClick}\n                onViewCountClick={handleViewCountClick}\n                renderActions={renderActions}\n                showUpdateInfo\n            />\n            <MCPReleaseStatus\n                status={server.serverStatus}\n                publishType={server.serverPublishType}\n                style={{\n                    position: 'absolute',\n                    top: 1,\n                    right: 1,\n                    zIndex: 1,\n                }}\n            />\n        </div>\n    );\n};\n\nexport default memo(SpaceMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(revision 8a318c2f200a8d033ac02625004d6755ce1504b3)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(date 1755257215800)
@@ -12,9 +12,10 @@
 import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
 
 const cardContainerStyle = css`
-    position: relative;
     &:hover {
-        z-index: 2;
+        .mcp-release-status {
+            z-index: 3;
+        }
     }
 `;
 
@@ -93,16 +94,19 @@
                 renderActions={renderActions}
                 showUpdateInfo
             />
-            <MCPReleaseStatus
-                status={server.serverStatus}
-                publishType={server.serverPublishType}
+            <div
                 style={{
                     position: 'absolute',
                     top: 1,
-                    right: 1,
+                    right: 7,
                     zIndex: 1,
                 }}
-            />
+            >
+                <MCPReleaseStatus
+                    status={server.serverStatus}
+                    publishType={server.serverPublishType}
+                />
+            </div>
         </div>
     );
 };
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {useCallback, useEffect, useMemo, useState} from 'react';\nimport {Flex, List, Space} from 'antd';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetMCPServerListByWorkspace} from '@/api/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPServerBase, MCPServerStatus} from '@/types/mcp/mcp';\nimport {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';\nimport {Gap} from '@/design/iplayground/Gap';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport SpaceMCPCard from './SpaceMCPCard';\nimport {useLoadMore} from './hooks';\n\nconst PAGE_SIZE = 18;\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst StyledLabel = styled.span`\n    display: inline-flex;\n    gap: 16px;\n    align-items: center;\n    color: #8F8F8F;\n    &: after{\n        content: '';\n        width: 1px;\n        height: 16px;\n        background: #D9D9D9;\n    }\n`;\n\nconst StyledList = styled(List<MCPServerBase>)`\n    .ant-list-grid .ant-col {\n        @media (min-width: 1600px) {\n            width: 25% !important;\n            max-width: 25% !important;\n            flex: 0 0 25% !important;\n        }\n    }\n`;\n\nconst MCPListPanel = () => {\n    const workspaceId = useMCPWorkspaceId();\n    const [selectedStatus, setSelectedStatus] = useState<MCPServerStatus | 'all'>('all');\n\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetMCPServerListByWorkspace({\n                workspaceId,\n                size: params.limit,\n                pn: params.current,\n                status: selectedStatus !== 'all' ? selectedStatus : undefined,\n            });\n        },\n        [workspaceId, selectedStatus]\n    );\n\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, PAGE_SIZE);\n\n    // TODO：接口上线后移除\n    const dataSource = useMemo(\n        () => {\n            if (selectedStatus === 'all') {\n                return list;\n            }\n            return list?.filter(item => item.serverStatus === selectedStatus);\n        },\n        [list, selectedStatus]\n    );\n    // const [newFirst, setNewFirst] = useState(true);\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <Space direction=\"vertical\">\n                <Space>\n                    <StyledLabel>MCP状态</StyledLabel>\n                    <RadioButtonGroup\n                        value={selectedStatus}\n                        onChange={setSelectedStatus}\n                        options={[\n                            {label: '全部', value: 'all'},\n                            {label: '已发布', value: 'release'},\n                            {label: '草稿', value: 'draft'},\n                        ]}\n                    />\n                </Space>\n                {/* <Space>\n                    <span>发布时间</span>\n                    <Button\n                        onClick={() => setNewFirst(value => !value)}\n                        type=\"text\"\n                    >\n                        {newFirst ? '新到旧' : '旧到新'} <StyledIcon newFirst={newFirst} />\n                    </Button>\n                </Space> */}\n            </Space>\n            <Gap />\n            <InfiniteScroll\n                // 挂一个key，避免切换type的时候出问题\n                key={selectedStatus}\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>上滑加载更多</div></Flex>}\n                // endMessage={<Divider plain>到底了</Divider>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <StyledList\n                        dataSource={dataSource}\n                        grid={{\n                            gutter: 20,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 4,\n                        }}\n                        rowKey=\"id\"\n                        renderItem={item => (\n                            <List.Item key={item.id}>\n                                <SpaceMCPCard refresh={refresh} server={item} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex align=\"center\" justify=\"center\">\n                                            <span>暂无MCP Server</span>\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal onSuccess={refresh} />\n        </Container>\n    );\n};\n\nexport default MCPListPanel;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(revision 8a318c2f200a8d033ac02625004d6755ce1504b3)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(date 1755254471916)
@@ -122,7 +122,7 @@
                     <StyledList
                         dataSource={dataSource}
                         grid={{
-                            gutter: 20,
+                            gutter: 12,
                             column: 2,
                             xs: 2,
                             sm: 3,
@@ -133,7 +133,7 @@
                         }}
                         rowKey="id"
                         renderItem={item => (
-                            <List.Item key={item.id}>
+                            <List.Item key={item.id} style={{margin: 0, paddingBottom: 12}}>
                                 <SpaceMCPCard refresh={refresh} server={item} />
                             </List.Item>
                         )}
Index: src/components/MCP/BaseMCPCard/styles.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {css} from '@emotion/css';\nimport {colors} from '@/constants/colors';\nimport bg from '@/assets/mcp/cardBg.png';\nimport vipbg from '@/assets/mcp/cardVipBg.png';\n\nexport const containerCss = ({official}: {official?: boolean}) => css`\n    padding: 17px 24px 12px;\n    position: relative;\n    height: 213px;\n    border-radius: 6px;\n    &:hover {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        z-index: 2;\n        background-color: #fff;\n        background-image: url(${official ? vipbg : bg});\n        background-repeat: no-repeat;\n        background-size: cover;\n        margin: 0 6px;\n        height: 264px;\n        .hover-actions {\n            opacity: 1;\n            min-height: 51px;\n            padding: 0 24px 20px;\n        }\n    }\n`;\n\nexport const hoverActionsStyle = css`\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    max-height: 0;\n    opacity: 0;\n    transition: all 0.3s ease;\n`;\n\nexport const DescriptionContainer = styled.div`\n    margin: 17px 0 12px;\n    font-size: 14px;\n    line-height: 22px;\n    position: relative;\n    height: 44px;\n    color: #545454;\n    overflow: hidden;\n`;\n\nexport const DescriptionText = styled.div`\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    word-break: break-word;\n    overflow: hidden;\n`;\n\nexport const EllipsisOverlay = styled.div`\n    position: absolute;\n    bottom: 9px;\n    right: 12px;\n    padding-left: 10px;\n    pointer-events: none;\n`;\n\nexport const cardContentStyle = {\n    overflow: 'hidden',\n    flex: 1,\n};\n\nexport const protocolTextStyle = {\n    color: '#8F8F8F',\n    fontSize: 12,\n    lineHeight: '18px',\n};\n\nexport const dividerStyle = {\n    margin: '16px 0 8px',\n};\n\nexport const statsContainerStyle = css`\n    color: ${colors['gray-7']};\n    font-size: 12px;\n    line-height: 20px;\n    transition: color 0.2s ease;\n    &:hover {\n        color: ${colors.primary};\n    }\n`;\n\nexport const iconStyle = {\n    width: 14,\n    height: 14,\n};\n\nexport const actionButtonStyle = {\n    fontSize: '12px',\n    lineHeight: '20px',\n    padding: 0,\n    height: 20,\n    gap: 4,\n    color: '#545454',\n};\n\nexport const fullWidthButtonStyle = {\n    background: 'var(--Tokens-, #F2F2F2)',\n    border: 'none',\n    width: '100%',\n    height: '32px',\n    gap: 4,\n};\n\nexport const formatCount = (count: number): string => {\n    if (count >= 10000) {\n        return `${Math.floor(count / 10000)}w+`;\n    }\n    if (count >= 1000) {\n        return `${Math.floor(count / 1000)}k+`;\n    }\n    return count.toString();\n};\n\nexport const actionButtonHoverStyle = css`\n    flex: 1;\n    border-radius: 4px;\n    border: 1px solid #BFBFBF;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.2s ease;\n\n    &:hover {\n        background-color: #E6E6E6 !important;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        transform: translateY(-1px);\n    }\n`;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/styles.ts b/src/components/MCP/BaseMCPCard/styles.ts
--- a/src/components/MCP/BaseMCPCard/styles.ts	(revision 8a318c2f200a8d033ac02625004d6755ce1504b3)
+++ b/src/components/MCP/BaseMCPCard/styles.ts	(date 1755256888303)
@@ -9,6 +9,7 @@
     position: relative;
     height: 213px;
     border-radius: 6px;
+    z-index: 0;
     &:hover {
         position: absolute;
         top: 0;
